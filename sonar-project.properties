# must be unique in a given SonarQube instance
sonar.projectKey=SDG

# --- optional properties ---

# defaults to project key
#sonar.projectName=My project
# defaults to 'not provided'
#sonar.projectVersion=1.0

# Path is relative to the sonar-project.properties file. Defaults to .
sonar.sources=gateway/GTWLib
#sonar.exclusions= **/*.h, **/*.hpp, **/*.xml
sonar.inclusions= **/.c, **/*.cpp, **/.h, **/*.hpp
# Encoding of the source code. Default is default system encoding
#sonar.sourceEncoding=UTF-8

# Start at the website and create your job on the server by generting a token.
# This is the token you need to run your job
sonar.login=****************************************
# Url of server
sonar.host.url=http://sonarqube:9000
# output of buildwrapper that we can once because this is a C/C++ project
sonar.cfamily.build-wrapper-output=bw-output

# turn on cache and give it a directory
sonar.cfamily.cache.enabled=true
sonar.cfamily.cache.path=sonarcache
